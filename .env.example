# NEXT Novel 官方网站环境变量配置示例

# 网站基础配置
NUXT_PUBLIC_SITE_URL=https://nextnovel.ai
NUXT_API_SECRET=your-api-secret-here

# Sanity CMS 配置
NUXT_PUBLIC_SANITY_PROJECT_ID=your-sanity-project-id
NUXT_PUBLIC_SANITY_DATASET=production
SANITY_TOKEN=your-sanity-token

# 分析和监控
NUXT_PUBLIC_MIXPANEL_TOKEN=your-mixpanel-token
NUXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# 第三方服务
NUXT_PUBLIC_CLOUDFLARE_ANALYTICS_TOKEN=your-cloudflare-analytics-token

# 开发环境配置
NODE_ENV=development
NUXT_DEVTOOLS=true
