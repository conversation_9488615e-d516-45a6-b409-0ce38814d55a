<template>
  <div class="overflow-hidden">
    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 overflow-hidden">
        <div
          ref="bgDecor1"
          class="absolute -top-40 -right-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl"
        ></div>
        <div
          ref="bgDecor2"
          class="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-500/20 rounded-full blur-3xl"
        ></div>
      </div>

      <div class="relative max-w-7xl mx-auto text-center hero-content">
        <div class="space-y-8">
          <!-- 主标题 -->
          <h1
            ref="heroTitle"
            class="text-hero font-bold leading-tight"
          >
            <span class="block text-gradient">
              全球首个 AIGC 多模态沉浸式故事共创平台
            </span>
          </h1>

          <!-- 副标题 -->
          <div class="text-xl sm:text-2xl text-slate-300 max-w-3xl mx-auto">
            <span>为</span>
            <span
              ref="roleText"
              class="text-gradient font-semibold"
            >{{ currentRole }}</span>
            <span>而生</span>
          </div>

          <!-- 描述 -->
          <p
            ref="heroDescription"
            class="text-lg text-slate-400 max-w-4xl mx-auto leading-relaxed"
          >
            NEXT Novel 结合前沿 AI 技术，让每个人都能创造属于自己的互动故事世界。无论你是内容创作者、游戏玩家还是开发者，都能在这里找到无限可能。
          </p>

          <!-- CTA 按钮 -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
            <NuxtLink
              ref="primaryCTA"
              to="/signup"
              class="btn-magnetic relative overflow-hidden px-8 py-4 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-xl"
            >
              开始创作
            </NuxtLink>
            <button
              ref="secondaryCTA"
              class="btn-magnetic px-8 py-4 border border-slate-600 text-slate-300 font-semibold rounded-xl hover:border-slate-500 hover:text-white transition-all duration-300"
              @click="openDemo"
            >
              观看演示
            </button>
          </div>
        </div>
      </div>

      <!-- 滚动指示器 -->
      <div
        ref="scrollIndicator"
        class="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div class="w-6 h-6 text-slate-400 cursor-pointer" @click="scrollToContent">↓</div>
      </div>
    </section>

    <!-- 三大支柱 Section -->
    <section ref="pillarsSection" class="py-24 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
          <h2
            ref="pillarsTitle"
            class="text-4xl sm:text-5xl font-bold text-gradient mb-4"
          >
            三大核心支柱
          </h2>
          <p
            ref="pillarsSubtitle"
            class="text-xl text-slate-400 max-w-3xl mx-auto"
          >
            重新定义故事创作的未来
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- AI 智能创作 -->
          <div
            ref="pillarCard1"
            class="pillar-card glass p-8 rounded-2xl card-hover"
          >
            <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mb-6">
              <div class="w-8 h-8 text-white">🧠</div>
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">
              AI 智能创作
            </h3>
            <p class="text-slate-400 leading-relaxed">
              先进的多模态 AI 引擎，支持文本、图像、音频的智能生成与编辑
            </p>
          </div>

          <!-- 沉浸式交互 -->
          <div
            ref="pillarCard2"
            class="pillar-card glass p-8 rounded-2xl card-hover"
          >
            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-6">
              <div class="w-8 h-8 text-white">🎮</div>
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">
              沉浸式交互
            </h3>
            <p class="text-slate-400 leading-relaxed">
              突破传统叙事边界，创造真正的互动式故事体验
            </p>
          </div>

          <!-- 共创生态 -->
          <div
            ref="pillarCard3"
            class="pillar-card glass p-8 rounded-2xl card-hover"
          >
            <div class="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center mb-6">
              <div class="w-8 h-8 text-white">👥</div>
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">
              共创生态
            </h3>
            <p class="text-slate-400 leading-relaxed">
              连接全球创作者，构建开放的故事创作与分享平台
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 在线演示 Section -->
    <section class="py-24 px-4 sm:px-6 lg:px-8 bg-slate-800/50">
      <div class="max-w-4xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-4xl sm:text-5xl font-bold text-gradient mb-4">
            在线体验
          </h2>
          <p class="text-xl text-slate-400">
            立即感受 NEXT Novel 的魅力
          </p>
        </div>

        <div class="glass p-8 rounded-2xl">
          <div class="text-center">
            <p class="text-lg text-slate-300 mb-6">
              AI 故事生成演示即将上线，敬请期待！
            </p>
            <button class="px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-xl">
              申请内测
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- 价格快照 Section -->
    <section class="py-24 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="text-4xl sm:text-5xl font-bold text-gradient mb-4">
            选择适合你的方案
          </h2>
          <p class="text-xl text-slate-400 max-w-3xl mx-auto">
            从个人创作者到企业用户，我们都有完美的解决方案
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- 基础方案 -->
          <div class="glass p-8 rounded-2xl">
            <h3 class="text-2xl font-bold text-white mb-4">TEST</h3>
            <div class="text-4xl font-bold text-white mb-4">免费</div>
            <p class="text-slate-400 mb-6">适合初学者和个人用户</p>
            <button class="w-full px-6 py-3 border border-slate-600 text-slate-300 font-semibold rounded-xl">
              开始使用
            </button>
          </div>

          <!-- 标准方案 -->
          <div class="glass p-8 rounded-2xl border-2 border-indigo-500">
            <h3 class="text-2xl font-bold text-white mb-4">Standard</h3>
            <div class="text-4xl font-bold text-white mb-4">¥99<span class="text-lg">/月</span></div>
            <p class="text-slate-400 mb-6">适合专业创作者</p>
            <button class="w-full px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-xl">
              开始使用
            </button>
          </div>

          <!-- 企业方案 -->
          <div class="glass p-8 rounded-2xl">
            <h3 class="text-2xl font-bold text-white mb-4">EX</h3>
            <div class="text-4xl font-bold text-white mb-4">¥299<span class="text-lg">/月</span></div>
            <p class="text-slate-400 mb-6">适合团队和企业</p>
            <button class="w-full px-6 py-3 border border-slate-600 text-slate-300 font-semibold rounded-xl">
              开始使用
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 导入动画组合式函数
const {
  animateRoleTransition,
  animateCardsOnScroll,
  createMagneticButton,
  createRippleEffect,
  animatePageLoad,
  shouldAnimate
} = useAnimations()

// 模板引用
const heroTitle = ref<HTMLElement>()
const roleText = ref<HTMLElement>()
const heroDescription = ref<HTMLElement>()
const primaryCTA = ref<HTMLElement>()
const secondaryCTA = ref<HTMLElement>()
const scrollIndicator = ref<HTMLElement>()
const bgDecor1 = ref<HTMLElement>()
const bgDecor2 = ref<HTMLElement>()
const pillarsSection = ref<HTMLElement>()
const pillarsTitle = ref<HTMLElement>()
const pillarsSubtitle = ref<HTMLElement>()
const pillarCard1 = ref<HTMLElement>()
const pillarCard2 = ref<HTMLElement>()
const pillarCard3 = ref<HTMLElement>()

// 角色轮播
const roles = ['创作者', '玩家', '开发者']
const currentRoleIndex = ref(0)
const currentRole = computed(() => roles[currentRoleIndex.value])

// 角色轮播逻辑
let roleInterval: NodeJS.Timeout
let cleanupFunctions: (() => void)[] = []

// 滚动到内容区域
const scrollToContent = () => {
  if (pillarsSection.value) {
    pillarsSection.value.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

// 打开演示模态框
const openDemo = () => {
  console.log('Open demo modal')
}

// 初始化动画
const initializeAnimations = async () => {
  if (!shouldAnimate()) return

  // 页面加载动画
  await animatePageLoad()

  // 设置磁性按钮效果
  if (primaryCTA.value) {
    const cleanup1 = createMagneticButton(primaryCTA.value)
    const cleanup2 = createRippleEffect(primaryCTA.value)
    if (cleanup1) cleanupFunctions.push(cleanup1)
    if (cleanup2) cleanupFunctions.push(cleanup2)
  }

  if (secondaryCTA.value) {
    const cleanup3 = createMagneticButton(secondaryCTA.value)
    if (cleanup3) cleanupFunctions.push(cleanup3)
  }

  // 设置卡片滚动动画
  const cards = [pillarCard1.value, pillarCard2.value, pillarCard3.value].filter(Boolean) as HTMLElement[]
  if (cards.length > 0) {
    animateCardsOnScroll(cards)
  }

  // 背景装饰动画
  if (bgDecor1.value && bgDecor2.value) {
    const { gsap } = await import('gsap')

    gsap.to(bgDecor1.value, {
      rotation: 360,
      duration: 20,
      ease: "none",
      repeat: -1
    })

    gsap.to(bgDecor2.value, {
      rotation: -360,
      duration: 25,
      ease: "none",
      repeat: -1
    })
  }

  // 滚动指示器动画
  if (scrollIndicator.value) {
    const { gsap } = await import('gsap')

    gsap.to(scrollIndicator.value, {
      y: 10,
      duration: 1,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true
    })
  }
}

// 角色切换动画
const animateRoleChange = async () => {
  if (roleText.value) {
    const newRole = roles[(currentRoleIndex.value + 1) % roles.length]
    await animateRoleTransition(roleText.value, newRole)
    currentRoleIndex.value = (currentRoleIndex.value + 1) % roles.length
  }
}

onMounted(async () => {
  // 等待 DOM 完全渲染
  await nextTick()

  // 初始化动画
  await initializeAnimations()

  // 开始角色轮播
  roleInterval = setInterval(animateRoleChange, 4000)
})

onUnmounted(() => {
  // 清理定时器
  if (roleInterval) {
    clearInterval(roleInterval)
  }

  // 清理事件监听器
  cleanupFunctions.forEach(cleanup => cleanup())
})

// SEO 配置
useHead({
  title: 'NEXT Novel - 全球首个 AIGC 多模态沉浸式故事共创平台',
  meta: [
    { name: 'description', content: 'NEXT Novel 结合前沿 AI 技术，让每个人都能创造属于自己的互动故事世界。支持文本、图像、音频的智能生成，打造真正的沉浸式故事体验。' }
  ]
})
</script>

<style scoped>
/* 自定义动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float {
  animation: float 6s ease-in-out infinite;
}
</style>
