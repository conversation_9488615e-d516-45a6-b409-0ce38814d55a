<template>
  <footer class="bg-slate-900 border-t border-slate-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 品牌信息 -->
        <div class="space-y-4">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">N</span>
            </div>
            <span class="text-xl font-bold text-gradient">NEXT Novel</span>
          </div>
          <p class="text-slate-400 leading-relaxed">
            {{ $t('footer.description') }}
          </p>
          <div class="flex space-x-4">
            <a
              href="#"
              class="text-slate-400 hover:text-white transition-colors duration-200"
              aria-label="Twitter"
            >
              <Icon name="simple-icons:twitter" class="w-5 h-5" />
            </a>
            <a
              href="#"
              class="text-slate-400 hover:text-white transition-colors duration-200"
              aria-label="GitHub"
            >
              <Icon name="simple-icons:github" class="w-5 h-5" />
            </a>
            <a
              href="#"
              class="text-slate-400 hover:text-white transition-colors duration-200"
              aria-label="Discord"
            >
              <Icon name="simple-icons:discord" class="w-5 h-5" />
            </a>
            <a
              href="#"
              class="text-slate-400 hover:text-white transition-colors duration-200"
              aria-label="Bilibili"
            >
              <Icon name="simple-icons:bilibili" class="w-5 h-5" />
            </a>
          </div>
        </div>

        <!-- 产品链接 -->
        <div class="space-y-4">
          <h3 class="text-white font-semibold">{{ $t('footer.links.product') }}</h3>
          <ul class="space-y-2">
            <li>
              <NuxtLink
                to="/product"
                class="text-slate-400 hover:text-white transition-colors duration-200"
              >
                产品功能
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/pricing"
                class="text-slate-400 hover:text-white transition-colors duration-200"
              >
                价格方案
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/docs"
                class="text-slate-400 hover:text-white transition-colors duration-200"
              >
                API 文档
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/changelog"
                class="text-slate-400 hover:text-white transition-colors duration-200"
              >
                更新日志
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- 公司链接 -->
        <div class="space-y-4">
          <h3 class="text-white font-semibold">{{ $t('footer.links.company') }}</h3>
          <ul class="space-y-2">
            <li>
              <NuxtLink
                to="/about"
                class="text-slate-400 hover:text-white transition-colors duration-200"
              >
                关于我们
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/careers"
                class="text-slate-400 hover:text-white transition-colors duration-200"
              >
                加入我们
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/blog"
                class="text-slate-400 hover:text-white transition-colors duration-200"
              >
                博客
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/contact"
                class="text-slate-400 hover:text-white transition-colors duration-200"
              >
                联系我们
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- 邮件订阅 -->
        <div class="space-y-4">
          <h3 class="text-white font-semibold">{{ $t('footer.newsletter.title') }}</h3>
          <p class="text-slate-400 text-sm">
            {{ $t('footer.newsletter.description') }}
          </p>
          <form @submit.prevent="subscribe" class="space-y-3">
            <div class="relative">
              <input
                v-model="email"
                type="email"
                :placeholder="$t('footer.newsletter.placeholder')"
                class="w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                required
              />
            </div>
            <button
              type="submit"
              class="w-full px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-indigo-500/25 transition-all duration-300 disabled:opacity-50"
              :disabled="isSubscribing"
            >
              <Icon
                v-if="isSubscribing"
                name="heroicons:arrow-path"
                class="w-4 h-4 mr-2 animate-spin inline"
              />
              {{ isSubscribing ? '订阅中...' : $t('footer.newsletter.subscribe') }}
            </button>
          </form>
          <div v-if="subscribeMessage" class="text-sm" :class="subscribeSuccess ? 'text-green-400' : 'text-red-400'">
            {{ subscribeMessage }}
          </div>
        </div>
      </div>

      <!-- 底部版权信息 -->
      <div class="mt-12 pt-8 border-t border-slate-800">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div class="text-slate-400 text-sm">
            {{ $t('footer.copyright') }}
          </div>
          <div class="flex space-x-6 text-sm">
            <NuxtLink
              to="/privacy"
              class="text-slate-400 hover:text-white transition-colors duration-200"
            >
              隐私政策
            </NuxtLink>
            <NuxtLink
              to="/terms"
              class="text-slate-400 hover:text-white transition-colors duration-200"
            >
              服务条款
            </NuxtLink>
            <NuxtLink
              to="/cookies"
              class="text-slate-400 hover:text-white transition-colors duration-200"
            >
              Cookie 政策
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
const { t } = useI18n()

const email = ref('')
const isSubscribing = ref(false)
const subscribeMessage = ref('')
const subscribeSuccess = ref(false)

const subscribe = async () => {
  if (!email.value) return

  isSubscribing.value = true
  subscribeMessage.value = ''

  try {
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 这里应该调用实际的订阅 API
    // await $fetch('/api/newsletter/subscribe', {
    //   method: 'POST',
    //   body: { email: email.value }
    // })

    subscribeMessage.value = t('footer.newsletter.success')
    subscribeSuccess.value = true
    email.value = ''
  } catch (error) {
    subscribeMessage.value = t('footer.newsletter.error')
    subscribeSuccess.value = false
  } finally {
    isSubscribing.value = false
    
    // 3秒后清除消息
    setTimeout(() => {
      subscribeMessage.value = ''
    }, 3000)
  }
}
</script>
