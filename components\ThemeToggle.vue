<template>
  <button
    class="theme-toggle"
    :aria-label="$t('common.toggleTheme')"
    @click="toggleTheme"
  >
    <div class="theme-toggle-icon">
      <Transition name="theme-icon" mode="out-in">
        <Icon
          v-if="isDarkMode"
          name="heroicons:sun"
          class="w-5 h-5"
        />
        <Icon
          v-else
          name="heroicons:moon"
          class="w-5 h-5"
        />
      </Transition>
    </div>
  </button>
</template>

<script setup lang="ts">
const appStore = useAppStore()
const { t } = useI18n()

const isDarkMode = computed(() => appStore.isDarkMode)

const toggleTheme = () => {
  appStore.toggleDarkMode()
}

// 监听系统主题变化
onMounted(() => {
  if (process.client) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    const handleChange = (e: MediaQueryListEvent) => {
      // 只有在用户没有手动设置过主题时才跟随系统
      const hasUserPreference = localStorage.getItem('darkMode')
      if (!hasUserPreference) {
        appStore.isDarkMode = e.matches
        document.documentElement.classList.toggle('dark', e.matches)
      }
    }
    
    mediaQuery.addEventListener('change', handleChange)
    
    onUnmounted(() => {
      mediaQuery.removeEventListener('change', handleChange)
    })
  }
})
</script>

<style scoped>
.theme-toggle {
  @apply relative p-2 rounded-lg text-text-muted hover:text-white hover:bg-surface-secondary transition-all duration-200 focus-ring;
}

.theme-toggle-icon {
  @apply relative overflow-hidden;
}

/* 主题图标切换动画 */
.theme-icon-enter-active,
.theme-icon-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-icon-enter-from {
  opacity: 0;
  transform: rotate(-90deg) scale(0.8);
}

.theme-icon-leave-to {
  opacity: 0;
  transform: rotate(90deg) scale(0.8);
}

.theme-icon-enter-to,
.theme-icon-leave-from {
  opacity: 1;
  transform: rotate(0deg) scale(1);
}

/* 响应 prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  .theme-icon-enter-active,
  .theme-icon-leave-active {
    transition: opacity 0.1s;
  }
  
  .theme-icon-enter-from,
  .theme-icon-leave-to {
    transform: none;
  }
}
</style>
