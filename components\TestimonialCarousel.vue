<template>
  <div class="relative">
    <!-- 轮播容器 -->
    <div class="overflow-hidden">
      <div
        class="flex transition-transform duration-500 ease-in-out"
        :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
      >
        <div
          v-for="(testimonial, index) in testimonials"
          :key="index"
          class="w-full flex-shrink-0 px-4"
        >
          <div class="glass p-8 rounded-2xl max-w-4xl mx-auto">
            <div class="text-center space-y-6">
              <!-- 引用内容 -->
              <blockquote class="text-xl text-slate-200 leading-relaxed">
                "{{ testimonial.content }}"
              </blockquote>
              
              <!-- 用户信息 -->
              <div class="flex items-center justify-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span class="text-white font-semibold text-lg">
                    {{ testimonial.name.charAt(0) }}
                  </span>
                </div>
                <div class="text-left">
                  <div class="font-semibold text-white">{{ testimonial.name }}</div>
                  <div class="text-sm text-slate-400">{{ testimonial.role }}</div>
                </div>
              </div>
              
              <!-- 评分 -->
              <div class="flex justify-center space-x-1">
                <Icon
                  v-for="i in 5"
                  :key="i"
                  name="heroicons:star"
                  class="w-5 h-5 text-yellow-400"
                  :class="i <= testimonial.rating ? 'text-yellow-400' : 'text-slate-600'"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导航按钮 -->
    <button
      class="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 glass rounded-full hover:bg-white/20 transition-all duration-200 focus-ring"
      @click="previousSlide"
    >
      <Icon name="heroicons:chevron-left" class="w-6 h-6 text-white" />
    </button>
    
    <button
      class="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 glass rounded-full hover:bg-white/20 transition-all duration-200 focus-ring"
      @click="nextSlide"
    >
      <Icon name="heroicons:chevron-right" class="w-6 h-6 text-white" />
    </button>

    <!-- 指示器 -->
    <div class="flex justify-center space-x-2 mt-8">
      <button
        v-for="(_, index) in testimonials"
        :key="index"
        class="w-3 h-3 rounded-full transition-all duration-200"
        :class="index === currentIndex 
          ? 'bg-indigo-500' 
          : 'bg-slate-600 hover:bg-slate-500'"
        @click="goToSlide(index)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const currentIndex = ref(0)

// 模拟推荐数据
const testimonials = [
  {
    content: "NEXT Novel 完全改变了我的创作方式。AI 助手不仅帮我生成了精彩的故事情节，还让我的创作效率提升了 300%。这是每个创作者都应该尝试的工具！",
    name: "张小明",
    role: "独立作家",
    rating: 5
  },
  {
    content: "作为一名游戏开发者，我一直在寻找能够快速生成高质量故事内容的工具。NEXT Novel 的多模态 AI 功能让我能够同时创作文本、设计角色和场景，大大缩短了开发周期。",
    name: "李开发",
    role: "游戏开发者",
    rating: 5
  },
  {
    content: "我的学生们都爱上了使用 NEXT Novel 创作故事。这个平台不仅激发了他们的创造力，还让他们学会了如何与 AI 协作。这是教育技术的未来！",
    name: "王老师",
    role: "中学语文教师",
    rating: 5
  },
  {
    content: "从传统的文字创作转向 AI 辅助创作，NEXT Novel 让这个过程变得非常自然。我现在可以专注于故事的核心创意，而让 AI 处理细节部分。合作效果超出预期！",
    name: "陈作家",
    role: "网络小说作者",
    rating: 4
  }
]

// 自动轮播
let autoplayInterval: NodeJS.Timeout

const startAutoplay = () => {
  autoplayInterval = setInterval(() => {
    nextSlide()
  }, 5000)
}

const stopAutoplay = () => {
  if (autoplayInterval) {
    clearInterval(autoplayInterval)
  }
}

// 导航方法
const nextSlide = () => {
  currentIndex.value = (currentIndex.value + 1) % testimonials.length
}

const previousSlide = () => {
  currentIndex.value = currentIndex.value === 0 
    ? testimonials.length - 1 
    : currentIndex.value - 1
}

const goToSlide = (index: number) => {
  currentIndex.value = index
}

// 生命周期
onMounted(() => {
  startAutoplay()
})

onUnmounted(() => {
  stopAutoplay()
})

// 鼠标悬停时暂停自动播放
const handleMouseEnter = () => {
  stopAutoplay()
}

const handleMouseLeave = () => {
  startAutoplay()
}
</script>

<style scoped>
/* 确保轮播容器的高度一致 */
.testimonial-slide {
  min-height: 200px;
}
</style>
