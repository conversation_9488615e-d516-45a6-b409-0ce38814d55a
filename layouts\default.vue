<template>
  <div class="min-h-screen bg-slate-900 text-slate-100 selection-primary">
    <!-- 导航栏 -->
    <nav
      ref="navbar"
      class="fixed top-0 left-0 right-0 z-50 glass border-b border-white/10 transition-all duration-300"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <div class="flex-shrink-0">
            <NuxtLink to="/" class="flex items-center space-x-2 group">
              <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">N</span>
              </div>
              <span class="text-xl font-bold text-gradient group-hover:scale-105 transition-transform duration-200">
                NEXT Novel
              </span>
            </NuxtLink>
          </div>

          <!-- 导航菜单 -->
          <div class="hidden lg:flex items-center space-x-8">
            <NuxtLink
              to="/"
              class="nav-link text-slate-300 hover:text-white transition-colors relative"
            >
              首页
            </NuxtLink>
            <NuxtLink
              to="/product"
              class="nav-link text-slate-300 hover:text-white transition-colors relative"
            >
              产品
            </NuxtLink>
            <NuxtLink
              to="/pricing"
              class="nav-link text-slate-300 hover:text-white transition-colors relative"
            >
              价格
            </NuxtLink>
            <NuxtLink
              to="/docs"
              class="nav-link text-slate-300 hover:text-white transition-colors relative"
            >
              文档
            </NuxtLink>
          </div>

          <!-- 右侧按钮 -->
          <div class="flex items-center space-x-4">
            <NuxtLink
              to="/login"
              class="px-4 py-2 text-sm font-medium text-slate-300 hover:text-white transition-colors"
            >
              登录
            </NuxtLink>
            <NuxtLink
              ref="navSignupBtn"
              to="/signup"
              class="btn-magnetic relative overflow-hidden px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-sm font-medium rounded-lg"
            >
              注册
            </NuxtLink>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main class="relative pt-16">
      <slot />
    </main>

    <!-- 页脚 -->
    <footer class="bg-slate-900 border-t border-slate-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <div class="flex items-center justify-center space-x-2 mb-4">
            <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">N</span>
            </div>
            <span class="text-xl font-bold text-gradient">NEXT Novel</span>
          </div>
          <p class="text-slate-400 mb-8">
            重新定义故事创作的未来
          </p>
          <div class="text-slate-400 text-sm">
            © 2025 NEXT Novel. 保留所有权利。
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// 导入动画组合式函数
const { createNavScrollAnimation, createMagneticButton, createRippleEffect } = useAnimations()

// 模板引用
const navbar = ref<HTMLElement>()
const navSignupBtn = ref<HTMLElement>()

// 清理函数
let cleanupFunctions: (() => void)[] = []

onMounted(async () => {
  await nextTick()

  // 设置导航栏滚动动画
  if (navbar.value) {
    const cleanup1 = createNavScrollAnimation(navbar.value)
    if (cleanup1) cleanupFunctions.push(cleanup1)
  }

  // 设置导航栏按钮动画
  if (navSignupBtn.value) {
    const cleanup2 = createMagneticButton(navSignupBtn.value)
    const cleanup3 = createRippleEffect(navSignupBtn.value)
    if (cleanup2) cleanupFunctions.push(cleanup2)
    if (cleanup3) cleanupFunctions.push(cleanup3)
  }
})

onUnmounted(() => {
  // 清理事件监听器
  cleanupFunctions.forEach(cleanup => cleanup())
})

// SEO 和 Meta 标签
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'format-detection', content: 'telephone=no' }
  ]
})
</script>

<style scoped>
/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
}

.page-enter-from {
  opacity: 0;
  transform: translateY(1rem);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-1rem);
}

/* 导航链接悬停效果 */
.nav-link {
  position: relative;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, #6366f1, #8b5cf6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
}

.nav-link:hover::before {
  width: 80%;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* 响应式动画优化 */
@media (prefers-reduced-motion: reduce) {
  .nav-link::before {
    transition: none;
  }

  .nav-link:hover {
    transition: none;
  }
}
</style>
