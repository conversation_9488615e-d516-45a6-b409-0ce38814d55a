<template>
  <div class="glass p-8 rounded-2xl">
    <div class="space-y-6">
      <!-- 输入区域 -->
      <div class="space-y-4">
        <div class="relative">
          <textarea
            v-model="prompt"
            :placeholder="$t('demo.placeholder')"
            class="w-full h-32 px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-xl text-white placeholder-slate-400 resize-none focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
            :disabled="isGenerating"
          />
          <div class="absolute bottom-3 right-3 text-xs text-slate-500">
            {{ prompt.length }}/500
          </div>
        </div>
        
        <div class="flex justify-between items-center">
          <div class="text-sm text-slate-400">
            <span v-if="!rateLimited">剩余次数: {{ remainingAttempts }}/15</span>
            <span v-else class="text-amber-400">{{ $t('demo.rateLimit') }}</span>
          </div>
          
          <button
            class="btn-magnetic px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
            :disabled="!canGenerate"
            @click="generateStory"
          >
            <Icon
              v-if="isGenerating"
              name="heroicons:arrow-path"
              class="w-5 h-5 mr-2 animate-spin"
            />
            <Icon
              v-else
              name="heroicons:sparkles"
              class="w-5 h-5 mr-2"
            />
            {{ isGenerating ? $t('demo.loading') : $t('demo.generate') }}
          </button>
        </div>
      </div>

      <!-- 生成结果 -->
      <div v-if="generatedStory || error" class="space-y-4">
        <div class="border-t border-slate-600 pt-6">
          <!-- 错误状态 -->
          <div
            v-if="error"
            class="p-4 bg-red-500/10 border border-red-500/20 rounded-xl"
          >
            <div class="flex items-center space-x-2 text-red-400">
              <Icon name="heroicons:exclamation-triangle" class="w-5 h-5" />
              <span class="font-medium">{{ $t('demo.error') }}</span>
            </div>
            <p class="mt-2 text-sm text-red-300">{{ error }}</p>
          </div>

          <!-- 成功状态 -->
          <div
            v-else-if="generatedStory"
            class="space-y-4"
          >
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-white">生成的故事</h3>
              <div class="flex space-x-2">
                <button
                  class="p-2 text-slate-400 hover:text-white transition-colors duration-200"
                  @click="copyStory"
                >
                  <Icon name="heroicons:clipboard-document" class="w-5 h-5" />
                </button>
                <button
                  class="p-2 text-slate-400 hover:text-white transition-colors duration-200"
                  @click="shareStory"
                >
                  <Icon name="heroicons:share" class="w-5 h-5" />
                </button>
              </div>
            </div>
            
            <div class="p-6 bg-slate-800/50 rounded-xl">
              <div class="prose prose-invert max-w-none">
                <p class="text-slate-200 leading-relaxed whitespace-pre-wrap">
                  {{ generatedStory }}
                </p>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex flex-col sm:flex-row gap-3">
              <button
                class="flex-1 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition-colors duration-200"
                @click="continueStory"
              >
                继续创作
              </button>
              <button
                class="flex-1 px-4 py-2 border border-slate-600 hover:border-slate-500 text-slate-300 hover:text-white font-medium rounded-lg transition-all duration-200"
                @click="regenerateStory"
              >
                重新生成
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 示例提示 -->
      <div v-if="!generatedStory && !error" class="space-y-3">
        <p class="text-sm text-slate-400">试试这些示例：</p>
        <div class="flex flex-wrap gap-2">
          <button
            v-for="example in examples"
            :key="example"
            class="px-3 py-1 text-sm bg-slate-800/50 hover:bg-slate-700 text-slate-300 hover:text-white rounded-full transition-all duration-200"
            @click="useExample(example)"
          >
            {{ example }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()

const prompt = ref('')
const generatedStory = ref('')
const error = ref('')
const isGenerating = ref(false)
const remainingAttempts = ref(15)
const rateLimited = ref(false)

// 示例提示
const examples = [
  '一个关于时间旅行的科幻故事',
  '魔法学院的冒险',
  '未来世界的机器人',
  '古代武侠传说',
  '太空探索任务'
]

// 检查是否可以生成
const canGenerate = computed(() => {
  return prompt.value.trim().length > 0 && 
         !isGenerating.value && 
         remainingAttempts.value > 0 && 
         !rateLimited.value
})

// 生成故事
const generateStory = async () => {
  if (!canGenerate.value) return

  isGenerating.value = true
  error.value = ''
  generatedStory.value = ''

  try {
    // 检查速率限制
    if (remainingAttempts.value <= 0) {
      rateLimited.value = true
      throw new Error(t('demo.rateLimit'))
    }

    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟生成的故事
    generatedStory.value = `基于您的主题"${prompt.value}"，这里是一个精彩的故事开头：

在一个充满神秘色彩的世界里，主人公踏上了一段不平凡的旅程。这个故事将带您体验前所未有的冒险，每一个选择都将影响故事的走向。

随着情节的展开，您将遇到各种有趣的角色，面临重要的决策，并见证一个独特世界的诞生。这只是开始，更多精彩内容等待您的探索...`

    remainingAttempts.value--
    
    // 保存到本地存储
    if (process.client) {
      const attempts = localStorage.getItem('demo-attempts') || '15'
      const newAttempts = Math.max(0, parseInt(attempts) - 1)
      localStorage.setItem('demo-attempts', newAttempts.toString())
      remainingAttempts.value = newAttempts
    }

  } catch (err: any) {
    error.value = err.message || t('demo.error')
  } finally {
    isGenerating.value = false
  }
}

// 使用示例
const useExample = (example: string) => {
  prompt.value = example
}

// 复制故事
const copyStory = async () => {
  if (process.client && generatedStory.value) {
    try {
      await navigator.clipboard.writeText(generatedStory.value)
      // 这里可以显示成功提示
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }
}

// 分享故事
const shareStory = () => {
  if (process.client && generatedStory.value) {
    if (navigator.share) {
      navigator.share({
        title: 'NEXT Novel 生成的故事',
        text: generatedStory.value
      })
    } else {
      copyStory()
    }
  }
}

// 继续创作
const continueStory = () => {
  prompt.value = '继续上面的故事...'
}

// 重新生成
const regenerateStory = () => {
  generateStory()
}

// 初始化剩余次数
onMounted(() => {
  if (process.client) {
    const stored = localStorage.getItem('demo-attempts')
    if (stored) {
      remainingAttempts.value = parseInt(stored)
    }
    
    // 检查是否需要重置（每天重置）
    const lastReset = localStorage.getItem('demo-last-reset')
    const today = new Date().toDateString()
    
    if (lastReset !== today) {
      remainingAttempts.value = 15
      rateLimited.value = false
      localStorage.setItem('demo-attempts', '15')
      localStorage.setItem('demo-last-reset', today)
    }
  }
})
</script>
