<template>
  <div class="user-menu" ref="menuRef">
    <!-- 用户头像按钮 -->
    <button
      class="user-avatar"
      :aria-label="$t('nav.userMenu')"
      @click="toggleMenu"
    >
      <img
        v-if="user.profile?.avatar"
        :src="user.profile.avatar"
        :alt="user.profile.name"
        class="w-8 h-8 rounded-full object-cover"
      />
      <div
        v-else
        class="w-8 h-8 rounded-full bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center text-white font-semibold text-sm"
      >
        {{ userInitials }}
      </div>
      
      <!-- 配额指示器 -->
      <div
        v-if="quotaPercentage < 20"
        class="absolute -top-1 -right-1 w-3 h-3 bg-warning-500 rounded-full border-2 border-background animate-pulse"
      ></div>
    </button>

    <!-- 下拉菜单 -->
    <Transition name="menu">
      <div
        v-if="isMenuOpen"
        class="user-dropdown"
        @click.stop
      >
        <!-- 用户信息 -->
        <div class="user-info">
          <div class="flex items-center space-x-3 p-4 border-b border-border">
            <img
              v-if="user.profile?.avatar"
              :src="user.profile.avatar"
              :alt="user.profile.name"
              class="w-10 h-10 rounded-full object-cover"
            />
            <div
              v-else
              class="w-10 h-10 rounded-full bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center text-white font-semibold"
            >
              {{ userInitials }}
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-white truncate">
                {{ user.profile?.name || $t('nav.user') }}
              </p>
              <p class="text-xs text-text-muted truncate">
                {{ user.profile?.email }}
              </p>
            </div>
          </div>
        </div>

        <!-- 配额信息 -->
        <div class="quota-info p-4 border-b border-border">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm text-text-muted">{{ $t('nav.quota') }}</span>
            <span class="text-sm font-medium text-white">
              {{ user.quota.remaining }}/{{ user.quota.total }}
            </span>
          </div>
          <div class="quota-bar">
            <div
              class="quota-fill"
              :style="{ width: `${quotaPercentage}%` }"
              :class="{
                'bg-success-500': quotaPercentage > 50,
                'bg-warning-500': quotaPercentage > 20 && quotaPercentage <= 50,
                'bg-error-500': quotaPercentage <= 20
              }"
            ></div>
          </div>
        </div>

        <!-- 菜单项 -->
        <div class="menu-items">
          <NuxtLink
            v-for="item in menuItems"
            :key="item.name"
            :to="item.href"
            class="menu-item"
            @click="closeMenu"
          >
            <Icon :name="item.icon" class="w-4 h-4" />
            <span>{{ $t(item.name) }}</span>
          </NuxtLink>
        </div>

        <!-- 登出按钮 -->
        <div class="p-2 border-t border-border">
          <button
            class="menu-item w-full text-error-500 hover:bg-error-500/10"
            @click="handleLogout"
          >
            <Icon name="heroicons:arrow-right-on-rectangle" class="w-4 h-4" />
            <span>{{ $t('nav.logout') }}</span>
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
const appStore = useAppStore()
const { t } = useI18n()

const menuRef = ref<HTMLElement>()
const isMenuOpen = ref(false)

const user = computed(() => appStore.user)
const quotaPercentage = computed(() => appStore.quotaPercentage)

const userInitials = computed(() => {
  const name = user.value.profile?.name || 'User'
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
})

const menuItems = [
  { name: 'nav.dashboard', href: '/dashboard', icon: 'heroicons:squares-2x2' },
  { name: 'nav.projects', href: '/projects', icon: 'heroicons:folder' },
  { name: 'nav.settings', href: '/settings', icon: 'heroicons:cog-6-tooth' },
  { name: 'nav.billing', href: '/billing', icon: 'heroicons:credit-card' },
  { name: 'nav.support', href: '/support', icon: 'heroicons:question-mark-circle' }
]

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

const closeMenu = () => {
  isMenuOpen.value = false
}

const handleLogout = async () => {
  try {
    // 调用登出API
    // await $fetch('/api/auth/logout', { method: 'POST' })
    
    appStore.setAuthenticated(false)
    appStore.showToast(t('nav.logoutSuccess'), 'success')
    
    // 重定向到首页
    await navigateTo('/')
  } catch (error) {
    appStore.showToast(t('nav.logoutError'), 'error')
  } finally {
    closeMenu()
  }
}

// 点击外部关闭菜单
onMounted(() => {
  const handleClickOutside = (event: Event) => {
    if (menuRef.value && !menuRef.value.contains(event.target as Node)) {
      closeMenu()
    }
  }

  document.addEventListener('click', handleClickOutside)
  
  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
})
</script>

<style scoped>
.user-menu {
  @apply relative;
}

.user-avatar {
  @apply relative p-1 rounded-full hover:bg-surface-secondary transition-colors duration-200 focus-ring;
}

.user-dropdown {
  @apply absolute right-0 top-full mt-2 w-64 glass border border-border rounded-xl shadow-lg z-50;
}

.menu-item {
  @apply flex items-center space-x-3 px-4 py-2 text-sm text-text-secondary hover:text-white hover:bg-surface-secondary transition-colors duration-200;
}

.quota-bar {
  @apply w-full h-2 bg-surface-secondary rounded-full overflow-hidden;
}

.quota-fill {
  @apply h-full transition-all duration-300 ease-out;
}

/* 菜单动画 */
.menu-enter-active,
.menu-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-enter-from,
.menu-leave-to {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

.menu-enter-to,
.menu-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}

@media (prefers-reduced-motion: reduce) {
  .menu-enter-active,
  .menu-leave-active {
    transition: opacity 0.1s;
  }
  
  .menu-enter-from,
  .menu-leave-to {
    transform: none;
  }
}
</style>
