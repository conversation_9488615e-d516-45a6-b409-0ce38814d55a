<template>
  <div class="space-y-8">
    <!-- 计费周期切换 -->
    <div class="flex justify-center">
      <div class="glass p-1 rounded-xl">
        <div class="flex space-x-1">
          <button
            class="px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200"
            :class="billingCycle === 'monthly' 
              ? 'bg-indigo-600 text-white shadow-lg' 
              : 'text-slate-400 hover:text-white'"
            @click="setBillingCycle('monthly')"
          >
            {{ $t('pricing.monthly') }}
          </button>
          <button
            class="px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 relative"
            :class="billingCycle === 'yearly' 
              ? 'bg-indigo-600 text-white shadow-lg' 
              : 'text-slate-400 hover:text-white'"
            @click="setBillingCycle('yearly')"
          >
            {{ $t('pricing.yearly') }}
            <span class="absolute -top-2 -right-2 px-2 py-0.5 bg-green-500 text-white text-xs rounded-full">
              {{ $t('pricing.save', { percent: 17 }) }}
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- 价格卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- TEST 方案 -->
      <div class="glass p-8 rounded-2xl border border-slate-600 hover:border-slate-500 transition-all duration-300">
        <div class="text-center space-y-4">
          <h3 class="text-2xl font-bold text-white">
            {{ $t('pricing.tiers.test.name') }}
          </h3>
          <p class="text-slate-400">
            {{ $t('pricing.tiers.test.description') }}
          </p>
          <div class="space-y-2">
            <div class="text-4xl font-bold text-white">
              {{ $t('pricing.tiers.test.price') }}
            </div>
          </div>
        </div>
        
        <div class="mt-8 space-y-4">
          <ul class="space-y-3">
            <li
              v-for="feature in $tm('pricing.tiers.test.features')"
              :key="feature"
              class="flex items-center space-x-3 text-slate-300"
            >
              <Icon name="heroicons:check" class="w-5 h-5 text-green-400 flex-shrink-0" />
              <span>{{ feature }}</span>
            </li>
          </ul>
          
          <button class="w-full mt-8 px-6 py-3 border border-slate-600 hover:border-slate-500 text-slate-300 hover:text-white font-semibold rounded-xl transition-all duration-300">
            {{ $t('pricing.cta') }}
          </button>
        </div>
      </div>

      <!-- Standard 方案 -->
      <div class="glass p-8 rounded-2xl border-2 border-indigo-500 hover:border-indigo-400 transition-all duration-300 relative transform hover:scale-105">
        <!-- 推荐标签 -->
        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <span class="px-4 py-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-sm font-semibold rounded-full">
            推荐
          </span>
        </div>
        
        <div class="text-center space-y-4">
          <h3 class="text-2xl font-bold text-white">
            {{ $t('pricing.tiers.standard.name') }}
          </h3>
          <p class="text-slate-400">
            {{ $t('pricing.tiers.standard.description') }}
          </p>
          <div class="space-y-2">
            <div class="text-4xl font-bold text-white">
              {{ billingCycle === 'monthly' 
                ? $t('pricing.tiers.standard.price') 
                : $t('pricing.tiers.standard.priceYearly') }}
            </div>
            <div class="text-slate-400 text-sm">
              {{ billingCycle === 'monthly' ? '每月' : '每年' }}
            </div>
          </div>
        </div>
        
        <div class="mt-8 space-y-4">
          <ul class="space-y-3">
            <li
              v-for="feature in $tm('pricing.tiers.standard.features')"
              :key="feature"
              class="flex items-center space-x-3 text-slate-300"
            >
              <Icon name="heroicons:check" class="w-5 h-5 text-green-400 flex-shrink-0" />
              <span>{{ feature }}</span>
            </li>
          </ul>
          
          <button class="w-full mt-8 px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-indigo-500/25 transition-all duration-300">
            {{ $t('pricing.cta') }}
          </button>
        </div>
      </div>

      <!-- EX 方案 -->
      <div class="glass p-8 rounded-2xl border border-slate-600 hover:border-slate-500 transition-all duration-300">
        <div class="text-center space-y-4">
          <h3 class="text-2xl font-bold text-white">
            {{ $t('pricing.tiers.ex.name') }}
          </h3>
          <p class="text-slate-400">
            {{ $t('pricing.tiers.ex.description') }}
          </p>
          <div class="space-y-2">
            <div class="text-4xl font-bold text-white">
              {{ billingCycle === 'monthly' 
                ? $t('pricing.tiers.ex.price') 
                : $t('pricing.tiers.ex.priceYearly') }}
            </div>
            <div class="text-slate-400 text-sm">
              {{ billingCycle === 'monthly' ? '每月' : '每年' }}
            </div>
          </div>
        </div>
        
        <div class="mt-8 space-y-4">
          <ul class="space-y-3">
            <li
              v-for="feature in $tm('pricing.tiers.ex.features')"
              :key="feature"
              class="flex items-center space-x-3 text-slate-300"
            >
              <Icon name="heroicons:check" class="w-5 h-5 text-green-400 flex-shrink-0" />
              <span>{{ feature }}</span>
            </li>
          </ul>
          
          <button class="w-full mt-8 px-6 py-3 border border-slate-600 hover:border-slate-500 text-slate-300 hover:text-white font-semibold rounded-xl transition-all duration-300">
            {{ $t('pricing.cta') }}
          </button>
        </div>
      </div>
    </div>

    <!-- 额外信息 -->
    <div class="text-center space-y-4 pt-8">
      <p class="text-slate-400">
        所有方案都包含 7 天免费试用，随时可以取消
      </p>
      <div class="flex justify-center space-x-8 text-sm text-slate-500">
        <span class="flex items-center space-x-1">
          <Icon name="heroicons:shield-check" class="w-4 h-4" />
          <span>安全支付</span>
        </span>
        <span class="flex items-center space-x-1">
          <Icon name="heroicons:arrow-path" class="w-4 h-4" />
          <span>随时取消</span>
        </span>
        <span class="flex items-center space-x-1">
          <Icon name="heroicons:chat-bubble-left-right" class="w-4 h-4" />
          <span>24/7 支持</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t, tm } = useI18n()

const billingCycle = ref<'monthly' | 'yearly'>('monthly')

const setBillingCycle = (cycle: 'monthly' | 'yearly') => {
  billingCycle.value = cycle
}
</script>

<style scoped>
/* 卡片悬停翻转效果 */
.pricing-card {
  perspective: 1000px;
}

.pricing-card:hover {
  transform: rotateY(5deg) rotateX(5deg);
}
</style>
