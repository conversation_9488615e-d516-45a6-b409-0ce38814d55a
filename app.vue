<template>
  <NuxtLayout>
    <NuxtPage />
  </NuxtLayout>
</template>

<script setup lang="ts">
// 全局 SEO 配置
useHead({
  title: 'NEXT Novel - 全球首个 AIGC 多模态沉浸式故事共创平台',
  meta: [
    { name: 'description', content: 'NEXT Novel 结合前沿 AI 技术，让每个人都能创造属于自己的互动故事世界。无论你是内容创作者、游戏玩家还是开发者，都能在这里找到无限可能。' },
    { name: 'keywords', content: 'AI故事创作,互动小说,AIGC,多模态AI,故事共创,内容创作平台' },
    { name: 'author', content: 'NEXT Novel Team' },
    { property: 'og:title', content: 'NEXT Novel - AI 驱动的故事创作平台' },
    { property: 'og:description', content: '全球首个 AIGC 多模态沉浸式故事共创平台，重新定义故事创作的未来' },
    { property: 'og:type', content: 'website' },
    { property: 'og:image', content: '/images/og-image.jpg' },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'NEXT Novel - AI 驱动的故事创作平台' },
    { name: 'twitter:description', content: '全球首个 AIGC 多模态沉浸式故事共创平台' },
    { name: 'twitter:image', content: '/images/og-image.jpg' }
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
    { rel: 'canonical', href: 'https://nextnovel.ai' }
  ]
})

// 结构化数据
useHead({
  script: [{
    type: 'application/ld+json',
    children: JSON.stringify({
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'NEXT Novel',
      description: '全球首个 AIGC 多模态沉浸式故事共创平台',
      url: 'https://nextnovel.ai',
      potentialAction: {
        '@type': 'SearchAction',
        target: 'https://nextnovel.ai/search?q={search_term_string}',
        'query-input': 'required name=search_term_string'
      }
    })
  }]
})
</script>
