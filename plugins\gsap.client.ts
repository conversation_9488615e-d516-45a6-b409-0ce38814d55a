import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { TextPlugin } from 'gsap/TextPlugin'
import { MotionPathPlugin } from 'gsap/MotionPathPlugin'

export default defineNuxtPlugin(() => {
  // 只在客户端注册插件
  if (process.client) {
    // 注册 GSAP 插件
    gsap.registerPlugin(ScrollTrigger, TextPlugin, MotionPathPlugin)
    
    // 商用许可证配置（PRD 中提到已购买）
    // gsap.config({ 
    //   license: "your-license-key-here",
    //   trialWarn: false 
    // })
    
    // 全局 GSAP 配置
    gsap.defaults({
      duration: 0.6,
      ease: "power2.out"
    })
    
    // ScrollTrigger 全局配置
    ScrollTrigger.defaults({
      toggleActions: "play none none reverse",
      markers: false // 生产环境设为 false
    })
    
    // 性能优化配置
    gsap.config({
      autoSleep: 60,
      force3D: true,
      nullTargetWarn: false
    })
    
    // 检测 prefers-reduced-motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)')
    
    if (prefersReducedMotion.matches) {
      // 如果用户偏好减少动画，则禁用所有动画
      gsap.globalTimeline.timeScale(0.01)
      ScrollTrigger.config({ limitCallbacks: true })
    }
    
    // 监听偏好变化
    prefersReducedMotion.addEventListener('change', (e) => {
      if (e.matches) {
        gsap.globalTimeline.timeScale(0.01)
        ScrollTrigger.config({ limitCallbacks: true })
      } else {
        gsap.globalTimeline.timeScale(1)
        ScrollTrigger.config({ limitCallbacks: false })
      }
    })
    
    // FPS 监控和自动降级
    let frameCount = 0
    let lastTime = performance.now()
    let lowFPSCount = 0
    let animationsDisabled = false
    
    function monitorFPS() {
      if (animationsDisabled) return
      
      const currentTime = performance.now()
      frameCount++
      
      if (currentTime - lastTime >= 1000) {
        const fps = frameCount
        frameCount = 0
        lastTime = currentTime
        
        if (fps < 30) {
          lowFPSCount++
          console.warn(`Low FPS detected: ${fps}`)
          
          if (lowFPSCount >= 3) {
            // 连续 3 秒低于 30 FPS，禁用动画
            console.warn('Disabling animations due to low FPS')
            gsap.globalTimeline.timeScale(0.01)
            animationsDisabled = true
            
            // 触发自定义事件通知应用
            window.dispatchEvent(new CustomEvent('gsap:low-fps', { 
              detail: { fps, disabled: true } 
            }))
            return
          }
        } else {
          lowFPSCount = 0
        }
      }
      
      requestAnimationFrame(monitorFPS)
    }
    
    // 开始 FPS 监控
    requestAnimationFrame(monitorFPS)
    
    // 页面可见性变化时的处理
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        gsap.globalTimeline.pause()
      } else {
        gsap.globalTimeline.resume()
      }
    })
    
    // 提供全局访问
    return {
      provide: {
        gsap,
        ScrollTrigger,
        TextPlugin,
        MotionPathPlugin
      }
    }
  }
})
