{"name": "next-novel-website", "private": true, "type": "module", "description": "NEXT Novel - 全球首个 AIGC 多模态沉浸式故事共创平台官方网站", "version": "1.0.0", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "lighthouse": "lhci autorun"}, "dependencies": {"@nuxt/content": "^3.6.0", "@nuxtjs/tailwindcss": "^6.12.1", "@nuxtjs/i18n": "^8.5.5", "@pinia/nuxt": "^0.5.5", "@vueuse/nuxt": "^11.2.0", "gsap": "^3.13.0", "nuxt": "^3.17.5", "pinia": "^2.2.6", "vue": "^3.5.16", "vue-router": "^4.5.1", "vue-i18n": "^10.0.4", "three": "^0.170.0", "@types/three": "^0.170.0"}, "devDependencies": {"@nuxt/eslint": "^0.7.4", "@nuxt/test-utils": "^3.15.2", "@playwright/test": "^1.49.1", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "eslint": "^9.17.0", "eslint-plugin-vue": "^9.31.0", "happy-dom": "^15.11.6", "lighthouse": "^12.2.1", "lhci": "^0.14.0", "prettier": "^3.4.2", "typescript": "^5.8.3", "vitest": "^2.1.8", "vue-tsc": "^2.2.10"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}