import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// 动画配置常量
export const ANIMATION_CONFIG = {
  durations: {
    fast: 0.2,
    normal: 0.3,
    slow: 0.5,
    hero: 1.2
  },
  easings: {
    power1: "power1.out",
    power2: "power2.out",
    power3: "power3.out",
    back: "back.out(1.7)",
    elastic: "elastic.out(1, 0.3)",
    bounce: "bounce.out"
  },
  stagger: {
    cards: 0.1,
    text: 0.02,
    items: 0.05
  }
}

export const useAnimations = () => {
  // 检查是否应该播放动画
  const shouldAnimate = () => {
    if (process.server) return false
    
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)')
    return !prefersReducedMotion.matches
  }

  // Hero 文字分割动画
  const animateHeroText = (element: HTMLElement, text: string) => {
    if (!shouldAnimate() || !element) return Promise.resolve()

    return new Promise<void>((resolve) => {
      // 分割文字为字符
      const chars = text.split('').map(char => 
        `<span class="inline-block">${char === ' ' ? '&nbsp;' : char}</span>`
      ).join('')
      
      element.innerHTML = chars
      const charElements = element.querySelectorAll('span')

      // 初始状态
      gsap.set(charElements, {
        opacity: 0,
        y: 50,
        rotationX: -90
      })

      // 动画序列
      const tl = gsap.timeline({
        onComplete: resolve
      })

      tl.to(charElements, {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: ANIMATION_CONFIG.durations.normal,
        ease: ANIMATION_CONFIG.easings.back,
        stagger: ANIMATION_CONFIG.stagger.text
      })

      return tl
    })
  }

  // 角色轮播动画
  const animateRoleTransition = (element: HTMLElement, newText: string) => {
    if (!shouldAnimate() || !element) return Promise.resolve()

    return new Promise<void>((resolve) => {
      const tl = gsap.timeline({
        onComplete: resolve
      })

      // 退出动画
      tl.to(element, {
        opacity: 0,
        y: -20,
        scale: 0.9,
        duration: ANIMATION_CONFIG.durations.fast,
        ease: ANIMATION_CONFIG.easings.power2
      })
      
      // 更新文字
      tl.call(() => {
        element.textContent = newText
      })
      
      // 进入动画
      tl.to(element, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: ANIMATION_CONFIG.durations.normal,
        ease: ANIMATION_CONFIG.easings.back
      })

      return tl
    })
  }

  // 滚动触发的卡片动画
  const animateCardsOnScroll = (cards: HTMLElement[]) => {
    if (!shouldAnimate() || !cards.length) return

    cards.forEach((card, index) => {
      gsap.fromTo(card, 
        {
          opacity: 0,
          y: 60,
          scale: 0.9,
          rotationY: -15
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          rotationY: 0,
          duration: ANIMATION_CONFIG.durations.slow,
          ease: ANIMATION_CONFIG.easings.back,
          delay: index * ANIMATION_CONFIG.stagger.cards,
          scrollTrigger: {
            trigger: card,
            start: "top 85%",
            end: "bottom 15%",
            toggleActions: "play none none reverse"
          }
        }
      )
    })
  }

  // 磁性按钮效果
  const createMagneticButton = (button: HTMLElement) => {
    if (!shouldAnimate() || !button) return

    let isHovering = false
    
    const handleMouseMove = (e: MouseEvent) => {
      if (!isHovering) return
      
      const rect = button.getBoundingClientRect()
      const centerX = rect.left + rect.width / 2
      const centerY = rect.top + rect.height / 2
      
      const deltaX = (e.clientX - centerX) * 0.3
      const deltaY = (e.clientY - centerY) * 0.3
      
      gsap.to(button, {
        x: deltaX,
        y: deltaY,
        duration: ANIMATION_CONFIG.durations.fast,
        ease: ANIMATION_CONFIG.easings.power2
      })
    }

    const handleMouseEnter = () => {
      isHovering = true
      gsap.to(button, {
        scale: 1.05,
        duration: ANIMATION_CONFIG.durations.normal,
        ease: ANIMATION_CONFIG.easings.back
      })
    }

    const handleMouseLeave = () => {
      isHovering = false
      gsap.to(button, {
        x: 0,
        y: 0,
        scale: 1,
        duration: ANIMATION_CONFIG.durations.normal,
        ease: ANIMATION_CONFIG.easings.elastic
      })
    }

    button.addEventListener('mousemove', handleMouseMove)
    button.addEventListener('mouseenter', handleMouseEnter)
    button.addEventListener('mouseleave', handleMouseLeave)

    // 返回清理函数
    return () => {
      button.removeEventListener('mousemove', handleMouseMove)
      button.removeEventListener('mouseenter', handleMouseEnter)
      button.removeEventListener('mouseleave', handleMouseLeave)
    }
  }

  // 涟漪效果
  const createRippleEffect = (button: HTMLElement) => {
    if (!shouldAnimate() || !button) return

    const handleClick = (e: MouseEvent) => {
      const rect = button.getBoundingClientRect()
      const size = Math.max(rect.width, rect.height)
      const x = e.clientX - rect.left - size / 2
      const y = e.clientY - rect.top - size / 2
      
      const ripple = document.createElement('div')
      ripple.style.cssText = `
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        left: ${x}px;
        top: ${y}px;
        width: ${size}px;
        height: ${size}px;
        pointer-events: none;
      `
      
      button.appendChild(ripple)
      
      gsap.to(ripple, {
        scale: 2,
        opacity: 0,
        duration: ANIMATION_CONFIG.durations.slow,
        ease: ANIMATION_CONFIG.easings.power2,
        onComplete: () => {
          ripple.remove()
        }
      })
    }

    button.addEventListener('click', handleClick)
    
    return () => {
      button.removeEventListener('click', handleClick)
    }
  }

  // 页面加载动画序列
  const animatePageLoad = () => {
    if (!shouldAnimate()) return Promise.resolve()

    return new Promise<void>((resolve) => {
      const tl = gsap.timeline({
        onComplete: resolve
      })

      // 导航栏动画
      tl.fromTo('nav', 
        { y: -100, opacity: 0 },
        { 
          y: 0, 
          opacity: 1, 
          duration: ANIMATION_CONFIG.durations.normal,
          ease: ANIMATION_CONFIG.easings.power2
        }
      )

      // Hero 内容动画
      tl.fromTo('.hero-content', 
        { y: 50, opacity: 0 },
        { 
          y: 0, 
          opacity: 1, 
          duration: ANIMATION_CONFIG.durations.slow,
          ease: ANIMATION_CONFIG.easings.power2
        }, 
        "-=0.2"
      )

      return tl
    })
  }

  // 导航栏滚动动画
  const createNavScrollAnimation = (nav: HTMLElement) => {
    if (!shouldAnimate() || !nav) return

    let lastScrollY = 0
    let ticking = false

    const updateNav = () => {
      const currentScrollY = window.scrollY
      
      if (currentScrollY > 100) {
        if (currentScrollY > lastScrollY) {
          // 向下滚动，隐藏导航栏
          gsap.to(nav, {
            y: -100,
            duration: ANIMATION_CONFIG.durations.normal,
            ease: ANIMATION_CONFIG.easings.power2
          })
        } else {
          // 向上滚动，显示导航栏
          gsap.to(nav, {
            y: 0,
            duration: ANIMATION_CONFIG.durations.normal,
            ease: ANIMATION_CONFIG.easings.power2
          })
        }
      } else {
        // 在顶部，始终显示
        gsap.to(nav, {
          y: 0,
          duration: ANIMATION_CONFIG.durations.normal,
          ease: ANIMATION_CONFIG.easings.power2
        })
      }
      
      lastScrollY = currentScrollY
      ticking = false
    }

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateNav)
        ticking = true
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }

  return {
    animateHeroText,
    animateRoleTransition,
    animateCardsOnScroll,
    createMagneticButton,
    createRippleEffect,
    animatePageLoad,
    createNavScrollAnimation,
    shouldAnimate,
    ANIMATION_CONFIG
  }
}
