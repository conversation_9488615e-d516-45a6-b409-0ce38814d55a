<template>
  <nav
    ref="navRef"
    class="fixed top-0 left-0 right-0 z-50 transition-transform duration-300 ease-out"
    :class="{
      '-translate-y-full': !appStore.isNavVisible,
      'translate-y-0': appStore.isNavVisible
    }"
  >
    <div class="glass border-b border-white/10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <div class="flex-shrink-0">
            <NuxtLink to="/" class="flex items-center space-x-2 group">
              <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">N</span>
              </div>
              <span class="text-xl font-bold text-gradient group-hover:scale-105 transition-transform duration-200">
                NEXT Novel
              </span>
            </NuxtLink>
          </div>

          <!-- 桌面端导航菜单 -->
          <div class="hidden lg:block">
            <div class="ml-10 flex items-baseline space-x-8">
              <NuxtLink
                v-for="item in navigationItems"
                :key="item.name"
                :to="item.href"
                class="nav-link"
                :class="{ 'nav-link-active': $route.path === item.href }"
              >
                {{ $t(item.name) }}
              </NuxtLink>
            </div>
          </div>

          <!-- 右侧操作区 -->
          <div class="flex items-center space-x-3">
            <!-- 主题切换 -->
            <ThemeToggle class="hidden sm:block" />

            <!-- 语言切换 -->
            <LanguageSwitcher class="hidden sm:block" />

            <!-- 用户配额显示 -->
            <div
              v-if="appStore.user.isAuthenticated"
              class="hidden sm:flex items-center space-x-2 px-3 py-1.5 rounded-full glass text-sm"
              :class="{
                'border-warning-500/50': appStore.isQuotaLow,
                'border-success-500/50': !appStore.isQuotaLow
              }"
            >
              <div
                class="w-2 h-2 rounded-full"
                :class="{
                  'bg-warning-500 animate-pulse': appStore.isQuotaLow,
                  'bg-success-500': !appStore.isQuotaLow
                }"
              ></div>
              <span class="font-medium">{{ appStore.user.quota.remaining }}/{{ appStore.user.quota.total }}</span>
            </div>

            <!-- 登录/注册按钮 -->
            <div v-if="!appStore.user.isAuthenticated" class="hidden sm:flex items-center space-x-2">
              <NuxtLink
                to="/login"
                class="px-4 py-2 text-sm font-medium text-text-muted hover:text-white transition-colors duration-200 focus-ring rounded-lg"
              >
                {{ $t('nav.login') }}
              </NuxtLink>
              <NuxtLink
                to="/signup"
                class="btn-magnetic btn-primary px-4 py-2 text-sm font-medium"
              >
                {{ $t('nav.signup') }}
              </NuxtLink>
            </div>

            <!-- 用户头像菜单 -->
            <UserMenu v-else class="hidden sm:block" />

            <!-- 移动端菜单按钮 -->
            <button
              class="lg:hidden p-2 rounded-lg text-text-muted hover:text-white hover:bg-surface-secondary focus-ring transition-colors duration-200"
              :aria-label="$t('nav.toggleMenu')"
              @click="appStore.toggleMobileMenu()"
            >
              <Icon
                :name="appStore.isMobileMenuOpen ? 'heroicons:x-mark' : 'heroicons:bars-3'"
                class="w-6 h-6"
              />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div
      v-if="appStore.isMobileMenuOpen"
      class="lg:hidden absolute top-full left-0 right-0 glass border-b border-white/10"
    >
      <div class="px-4 py-4 space-y-2">
        <NuxtLink
          v-for="item in navigationItems"
          :key="item.name"
          :to="item.href"
          class="block px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-800 transition-colors duration-200"
          :class="{ 'text-white bg-slate-800': $route.path === item.href }"
        >
          {{ $t(item.name) }}
        </NuxtLink>
        
        <!-- 移动端用户操作 -->
        <div class="pt-4 border-t border-border">
          <!-- 用户配额显示 (移动端) -->
          <div
            v-if="appStore.user.isAuthenticated"
            class="mb-4 p-3 glass rounded-lg"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-text-muted">{{ $t('nav.quota') }}</span>
              <span class="text-sm font-medium text-white">
                {{ appStore.user.quota.remaining }}/{{ appStore.user.quota.total }}
              </span>
            </div>
            <div class="w-full h-2 bg-surface-secondary rounded-full overflow-hidden">
              <div
                class="h-full transition-all duration-300"
                :style="{ width: `${appStore.quotaPercentage}%` }"
                :class="{
                  'bg-success-500': appStore.quotaPercentage > 50,
                  'bg-warning-500': appStore.quotaPercentage > 20 && appStore.quotaPercentage <= 50,
                  'bg-error-500': appStore.quotaPercentage <= 20
                }"
              ></div>
            </div>
          </div>

          <div v-if="!appStore.user.isAuthenticated" class="space-y-2 mb-4">
            <NuxtLink
              to="/login"
              class="block px-3 py-2 rounded-lg text-base font-medium text-text-muted hover:text-white hover:bg-surface-secondary transition-colors duration-200"
              @click="appStore.closeMobileMenu()"
            >
              {{ $t('nav.login') }}
            </NuxtLink>
            <NuxtLink
              to="/signup"
              class="block px-3 py-2 rounded-lg text-base font-medium btn-primary text-center"
              @click="appStore.closeMobileMenu()"
            >
              {{ $t('nav.signup') }}
            </NuxtLink>
          </div>

          <!-- 移动端工具栏 -->
          <div class="flex items-center justify-between pt-2">
            <ThemeToggle />
            <LanguageSwitcher />
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { useAppStore } from '~/stores/app'
import { useAnimations } from '~/composables/useAnimations'

const appStore = useAppStore()
const { t } = useI18n()
const { createNavScrollAnimation } = useAnimations()

const navRef = ref<HTMLElement>()

// 导航菜单项
const navigationItems = [
  { name: 'nav.home', href: '/' },
  { name: 'nav.product', href: '/product' },
  { name: 'nav.creators', href: '/creators' },
  { name: 'nav.pricing', href: '/pricing' },
  { name: 'nav.docs', href: '/docs' },
  { name: 'nav.community', href: '/community' }
]

// 监听路由变化，关闭移动端菜单
const route = useRoute()
watch(() => route.path, () => {
  appStore.closeMobileMenu()
})

// 初始化滚动动画
onMounted(() => {
  if (navRef.value) {
    const cleanup = createNavScrollAnimation(navRef.value)

    onUnmounted(() => {
      if (cleanup) cleanup()
    })
  }
})
</script>

<style scoped>
.nav-link {
  @apply px-3 py-2 rounded-lg text-sm font-medium text-text-muted hover:text-white transition-all duration-200 relative focus-ring;
}

.nav-link::after {
  content: '';
  @apply absolute bottom-0 left-1/2 w-0 h-0.5 bg-gradient-to-r from-primary-500 to-secondary-500 transition-all duration-300;
  transform: translateX(-50%);
  border-radius: 2px;
}

.nav-link:hover::after,
.nav-link-active::after {
  @apply w-full;
}

.nav-link-active {
  @apply text-white;
}

/* 移动端菜单项样式 */
.mobile-nav-link {
  @apply block px-3 py-2 rounded-lg text-base font-medium text-text-muted hover:text-white hover:bg-surface-secondary transition-colors duration-200;
}

.mobile-nav-link.active {
  @apply text-white bg-surface-secondary;
}

/* 响应式优化 */
@media (max-width: 640px) {
  .nav-link {
    @apply px-2 py-1.5 text-xs;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .nav-link {
    @apply border border-transparent hover:border-white;
  }

  .nav-link-active {
    @apply border-white;
  }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
  .nav-link::after {
    transition: width 0.1s;
  }
}
</style>
