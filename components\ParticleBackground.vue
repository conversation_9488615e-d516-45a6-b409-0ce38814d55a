<template>
  <div class="particle-background" ref="containerRef">
    <canvas
      ref="canvasRef"
      class="absolute inset-0 w-full h-full"
      :class="{ 'opacity-0': !shouldAnimate }"
    />
  </div>
</template>

<script setup lang="ts">
interface Particle {
  x: number
  y: number
  vx: number
  vy: number
  size: number
  opacity: number
  color: string
}

const containerRef = ref<HTMLElement>()
const canvasRef = ref<HTMLCanvasElement>()
const appStore = useAppStore()

const shouldAnimate = computed(() => appStore.shouldAnimate)
const particles = ref<Particle[]>([])
const animationId = ref<number>()

const colors = [
  'rgba(99, 102, 241, 0.6)',   // primary
  'rgba(139, 92, 246, 0.6)',   // secondary  
  'rgba(6, 182, 212, 0.6)',    // accent
]

const createParticle = (canvas: HTMLCanvasElement): Particle => {
  return {
    x: Math.random() * canvas.width,
    y: Math.random() * canvas.height,
    vx: (Math.random() - 0.5) * 0.5,
    vy: (Math.random() - 0.5) * 0.5,
    size: Math.random() * 2 + 1,
    opacity: Math.random() * 0.5 + 0.2,
    color: colors[Math.floor(Math.random() * colors.length)]
  }
}

const initParticles = (canvas: HTMLCanvasElement) => {
  const particleCount = Math.min(50, Math.floor((canvas.width * canvas.height) / 15000))
  particles.value = Array.from({ length: particleCount }, () => createParticle(canvas))
}

const updateParticle = (particle: Particle, canvas: HTMLCanvasElement) => {
  particle.x += particle.vx
  particle.y += particle.vy

  // 边界检测
  if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1
  if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1

  // 保持在画布内
  particle.x = Math.max(0, Math.min(canvas.width, particle.x))
  particle.y = Math.max(0, Math.min(canvas.height, particle.y))
}

const drawParticle = (ctx: CanvasRenderingContext2D, particle: Particle) => {
  ctx.save()
  ctx.globalAlpha = particle.opacity
  ctx.fillStyle = particle.color
  ctx.beginPath()
  ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
  ctx.fill()
  ctx.restore()
}

const drawConnections = (ctx: CanvasRenderingContext2D, particles: Particle[]) => {
  const maxDistance = 120
  
  for (let i = 0; i < particles.length; i++) {
    for (let j = i + 1; j < particles.length; j++) {
      const dx = particles[i].x - particles[j].x
      const dy = particles[i].y - particles[j].y
      const distance = Math.sqrt(dx * dx + dy * dy)
      
      if (distance < maxDistance) {
        const opacity = (1 - distance / maxDistance) * 0.2
        ctx.save()
        ctx.globalAlpha = opacity
        ctx.strokeStyle = 'rgba(99, 102, 241, 0.3)'
        ctx.lineWidth = 1
        ctx.beginPath()
        ctx.moveTo(particles[i].x, particles[i].y)
        ctx.lineTo(particles[j].x, particles[j].y)
        ctx.stroke()
        ctx.restore()
      }
    }
  }
}

const animate = () => {
  if (!canvasRef.value || !shouldAnimate.value) return

  const canvas = canvasRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 更新和绘制粒子
  particles.value.forEach(particle => {
    updateParticle(particle, canvas)
    drawParticle(ctx, particle)
  })

  // 绘制连接线
  drawConnections(ctx, particles.value)

  animationId.value = requestAnimationFrame(animate)
}

const resizeCanvas = () => {
  if (!canvasRef.value || !containerRef.value) return

  const canvas = canvasRef.value
  const container = containerRef.value
  const rect = container.getBoundingClientRect()

  canvas.width = rect.width
  canvas.height = rect.height

  // 重新初始化粒子
  initParticles(canvas)
}

const startAnimation = () => {
  if (!shouldAnimate.value) return
  
  resizeCanvas()
  animate()
}

const stopAnimation = () => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = undefined
  }
}

// 监听动画偏好变化
watch(shouldAnimate, (newValue) => {
  if (newValue) {
    startAnimation()
  } else {
    stopAnimation()
  }
})

onMounted(() => {
  if (process.client) {
    // 延迟启动以确保DOM完全渲染
    nextTick(() => {
      startAnimation()
    })

    // 监听窗口大小变化
    const handleResize = debounce(resizeCanvas, 250)
    window.addEventListener('resize', handleResize)

    onUnmounted(() => {
      stopAnimation()
      window.removeEventListener('resize', handleResize)
    })
  }
})

// 防抖函数
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout
  return ((...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), wait)
  }) as T
}
</script>

<style scoped>
.particle-background {
  @apply absolute inset-0 overflow-hidden pointer-events-none;
}

canvas {
  transition: opacity 0.5s ease-in-out;
}

/* 减少动画时隐藏 */
@media (prefers-reduced-motion: reduce) {
  canvas {
    display: none;
  }
}
</style>
