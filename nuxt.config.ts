// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },

  // 模块配置
  modules: [
    '@nuxtjs/tailwindcss',
    '@vueuse/nuxt',
    '@nuxtjs/i18n',
    '@pinia/nuxt',
    '@nuxt/content',
    '@nuxt/eslint'
  ],

  // TypeScript 配置
  typescript: {
    strict: true,
    typeCheck: true
  },

  // CSS 配置
  css: [
    '~/assets/css/main.css'
  ],
  // 国际化配置
  i18n: {
    locales: [
      { code: 'zh-CN', name: '简体中文', file: 'zh-CN.json' },
      { code: 'en', name: 'English', file: 'en.json' }
    ],
    defaultLocale: 'zh-CN',
    langDir: 'locales/',
    strategy: 'prefix_except_default',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root'
    }
  },





  // Tailwind CSS 配置
  tailwindcss: {
    configPath: './tailwind.config.js'
  },
  // 内容管理配置
  content: {
    markdown: {
      anchorLinks: false
    },
    highlight: {
      theme: 'github-dark'
    }
  },



  // 性能优化配置
  nitro: {
    compressPublicAssets: true,
    minify: true,
    prerender: {
      routes: ['/sitemap.xml']
    }
  },

  // 构建配置
  build: {
    transpile: ['gsap', 'three']
  },

  // 实验性功能
  experimental: {
    payloadExtraction: false,
    viewTransition: true
  },



  // Vite 配置
  vite: {
    server: {
      fs: {
        allow: [
          // 允许访问项目根目录
          '.',
          // 允许访问父级 node_modules
          '../node_modules'
        ]
      }
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            'gsap': ['gsap'],
            'three': ['three']
          }
        }
      }
    }
  },

  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅服务端可用）
    apiSecret: process.env.NUXT_API_SECRET || '',
    sanityToken: process.env.SANITY_TOKEN || '',

    // 公共配置（客户端和服务端都可用）
    public: {
      apiBase: '/api',
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3000',
      sanityProjectId: process.env.NUXT_PUBLIC_SANITY_PROJECT_ID || '',
      sanityDataset: process.env.NUXT_PUBLIC_SANITY_DATASET || 'production'
    }
  },

  // 应用配置
  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'NEXT Novel - 全球首个 AIGC 多模态沉浸式故事共创平台',
      meta: [
        { name: 'description', content: 'NEXT Novel 结合前沿 AI 技术，让每个人都能创造属于自己的互动故事世界。支持文本、图像、音频的智能生成，打造真正的沉浸式故事体验。' },
        { name: 'theme-color', content: '#0f172a' },
        { name: 'msapplication-TileColor', content: '#0f172a' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
        { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap' }
      ]
    }
  },

  // 路由配置
  router: {
    options: {
      scrollBehaviorType: 'smooth'
    }
  }
})