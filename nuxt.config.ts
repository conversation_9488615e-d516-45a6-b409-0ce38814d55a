// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },

  // 模块配置
  modules: [
    '@nuxtjs/tailwindcss',
    '@vueuse/nuxt'
  ],

  // TypeScript 配置
  typescript: {
    strict: false,
    typeCheck: false
  },

  // CSS 配置
  css: [
    '~/assets/css/main.css'
  ],





  // Tailwind CSS 配置
  tailwindcss: {
    configPath: './tailwind.config.js'
  },



  // 性能优化配置
  nitro: {
    compressPublicAssets: true,
    minify: true
  },

  // 构建配置
  build: {
    transpile: ['gsap']
  },



  // Vite 配置
  vite: {
    server: {
      fs: {
        allow: [
          // 允许访问项目根目录
          '.',
          // 允许访问父级 node_modules
          '../node_modules'
        ]
      }
    }
  },

  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅服务端可用）
    apiSecret: '',

    // 公共配置（客户端和服务端都可用）
    public: {
      apiBase: '/api',
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    }
  }
})